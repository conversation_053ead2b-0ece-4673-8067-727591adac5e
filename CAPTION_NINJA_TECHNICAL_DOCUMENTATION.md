# CAPTION.Ninja Technical Documentation

## 1. Project Overview

CAPTION.Ninja is a free-to-use, browser-based captioning, transcription, and real-time translation tool designed for live streams, presentations, and accessibility applications. The system leverages browser-native speech recognition APIs to provide real-time speech-to-text conversion with optional translation capabilities.

### Key Features
- Real-time speech-to-text transcription using browser APIs
- WebSocket-based room system for multi-client communication
- Translation support via Mozilla Bergamot (local) and Google Cloud API
- Text-to-speech integration with multiple providers
- Export capabilities (SRT, WebVTT, plain text)
- Overlay system for OBS/streaming integration
- Manual text entry mode for pre-written content

## 2. System Architecture

### 2.1 Overall Architecture
```
┌─────────────────┐    WebSocket     ┌─────────────────┐
│  Capture Pages  │ ◄──────────────► │  WebSocket      │
│  (Speech Input) │                  │  Server         │
└─────────────────┘                  │ (api.caption.   │
                                     │  ninja:443)     │
┌─────────────────┐                  │                 │
│  Overlay Pages  │ ◄──────────────► │                 │
│  (Display)      │                  └─────────────────┘
└─────────────────┘

┌─────────────────┐    Web Workers   ┌─────────────────┐
│  Translation    │ ◄──────────────► │  Bergamot       │
│  Processing     │                  │  Worker         │
└─────────────────┘                  └─────────────────┘
```

### 2.2 Data Flow
1. **Speech Capture**: <PERSON><PERSON><PERSON>'s `webkitSpeechRecognition` API captures audio
2. **Text Processing**: Speech is converted to text with interim and final results
3. **WebSocket Transmission**: Text data is sent to WebSocket server with room ID
4. **Distribution**: Server broadcasts messages to all clients in the same room
5. **Display/Translation**: Overlay pages receive and optionally translate text
6. **Output**: Final captions are displayed with customizable styling

### 2.3 Room-Based Routing System
- Each session uses a unique room ID (generated or user-specified)
- WebSocket messages include room identification: `{"join": roomID}`
- Server routes messages only to clients in the same room
- Room IDs can be shared via URL parameters for multi-client sessions

## 3. Core Components Analysis

### 3.1 Capture Pages

#### 3.1.1 index.html - Basic Capture Page
**Purpose**: Simple speech-to-text capture with minimal UI
**Key Features**:
- Browser speech recognition setup
- Real-time transcription display
- WebSocket connection management
- SRT export functionality
- Basic pause/resume controls

**JavaScript Functions**:
- `setup()`: Initializes speech recognition
- `connectWebSocket()`: Establishes WebSocket connection with retry logic
- `detectTextDirection()`: Handles RTL text detection
- `download()`: Handles file downloads

#### 3.1.2 capture-pro.html - Enhanced Capture Page
**Purpose**: Professional capture interface with advanced features
**Key Features**:
- Pause-based segmentation for cleaner SRT cues
- Smart line wrapping for exports
- Multiple export formats (SRT, WebVTT, Plain Text)
- Autosave and session recovery
- Keyboard shortcuts (Space, Ctrl+S, Ctrl+L, M)
- Status indicators for recognition and WebSocket connection
- Profanity masking option

**Enhanced Functions**:
- `flushSegment()`: Manages text segmentation
- `wrapLines()`: Handles intelligent line wrapping
- `saveSnapshot()`: Implements autosave functionality

#### 3.1.3 translate.html - Translation Capture Page
**Purpose**: Real-time translation with speech input
**Key Features**:
- Integrated Mozilla Bergamot translation
- Language pair selection
- Translation queue management
- Google Cloud API integration option
- Context-aware translation

**Translation Functions**:
- `translateCall()`: Manages translation requests
- `processTranslationQueue()`: Handles translation queuing
- `loadModel()`: Loads Bergamot translation models

### 3.2 Overlay Pages

#### 3.2.1 overlay.html - Standard Overlay
**Purpose**: Basic caption display for streaming/presentation
**Key Features**:
- Real-time caption display
- Translation support via URL parameters
- TTS integration
- Customizable styling via CSS
- Auto-hide functionality

#### 3.2.2 overlay_enhanced.html - Enhanced Overlay
**Purpose**: Advanced overlay with additional features
**Key Features**:
- Enhanced styling options
- Better mobile responsiveness
- Advanced TTS configuration
- Improved translation handling

#### 3.2.3 overlay_roll.html - Rolling Credits Overlay
**Purpose**: Credits-style scrolling text display
**Key Features**:
- Vertical scrolling text
- Auto-speed adjustment based on live STT
- Manual speed control
- Font size customization

### 3.3 Supporting Components

#### 3.3.1 worker.js - Translation Worker
**Purpose**: Background translation processing using Mozilla Bergamot
**Key Functions**:
- `constructTranslationService()`: Initializes translation service
- `constructTranslationModel()`: Loads language models
- `translate()`: Performs translation with pivot language support
- `downloadAsArrayBuffer()`: Downloads model files

#### 3.3.2 tts-integration.js - Text-to-Speech Integration
**Purpose**: Lightweight TTS wrapper for multiple providers
**Key Features**:
- Multiple TTS provider support (browser, Google, ElevenLabs, etc.)
- tts.rocks engine integration
- Streaming TTS support
- Configuration via URL parameters

#### 3.3.3 security-utils.js - Security Utilities
**Purpose**: Security warnings for insecure room IDs
**Key Functions**:
- `isInsecureStreamId()`: Detects common insecure patterns
- `showSecurityWarning()`: Displays security warnings
- `injectSecurityStyles()`: Adds warning styles

## 4. WebSocket Communication Protocol

### 4.1 Connection Setup
```javascript
socket = new WebSocket("wss://api.caption.ninja:443");
socket.onopen = function() {
    socket.send(JSON.stringify({"join": roomID}));
};
```

### 4.2 Message Formats

#### 4.2.1 Join Room Message
```json
{"join": "room_id_string"}
```

#### 4.2.2 Caption Messages
```json
{
    "msg": true,
    "final": "Final transcribed text",
    "id": 123,
    "label": "Speaker Name",
    "ln": "en-US"
}
```

#### 4.2.3 Interim Messages
```json
{
    "msg": true,
    "interm": "Interim text being spoken...",
    "id": 123,
    "label": "Speaker Name",
    "ln": "en-US"
}
```

### 4.3 Connection Management
- Automatic reconnection with exponential backoff
- Retry logic with configurable delays
- Connection state monitoring
- Error handling for network issues

## 5. Translation System

### 5.1 Mozilla Bergamot (Local Translation)
**Supported Languages**: 17 languages (bg, cs, nl, en, et, de, fr, is, it, nb, nn, fa, pl, pt, ru, es, uk)
**Implementation**:
- WebAssembly-based translation models
- Client-side processing (privacy-friendly)
- Model caching and management
- Pivot translation through English for unsupported pairs

### 5.2 Google Cloud Translation API
**Features**:
- 100+ language support
- Context-aware translation
- Adjustable context size
- Translation caching
- Rate limiting and error handling

### 5.3 Translation Parameters
| Parameter | Description | Example |
|-----------|-------------|---------|
| `translate=XX` | Target language | `&translate=es` |
| `fromlang=XX` | Source language override | `&fromlang=en` |
| `googlekey=KEY` | Google API key | `&googlekey=YOUR_KEY` |
| `context=1` | Enable context-aware translation | `&context=1` |
| `contextsize=N` | Context history size | `&contextsize=5` |
| `forcelocal=1` | Force local translation | `&forcelocal=1` |

## 6. Speech Recognition Implementation

### 6.1 Browser Compatibility
- **Primary**: Chrome, Edge (webkitSpeechRecognition)
- **Limited**: Firefox (no built-in speech recognition)
- **Fallback**: Manual text entry mode

### 6.2 Recognition Configuration
```javascript
recognition = new webkitSpeechRecognition();
recognition.lang = myLang;           // Language setting
recognition.continuous = true;       // Continuous recognition
recognition.interimResults = true;   // Interim results enabled
```

### 6.3 Event Handling
- `onstart`: Recognition started
- `onresult`: Text results (interim/final)
- `onerror`: Error handling
- `onend`: Recognition ended (auto-restart)

## 7. Export Capabilities

### 7.1 SRT Format
- Proper timestamp calculation
- Configurable line length
- Speaker labels support
- Pause-based segmentation

### 7.2 WebVTT Format
- Web-compatible subtitle format
- Styling support
- Cue positioning

### 7.3 Plain Text
- Simple text export
- Timestamp options
- Speaker identification

## 8. URL Parameters Reference

### 8.1 Common Parameters
| Parameter | Description | Default | Example |
|-----------|-------------|---------|---------|
| `room` | Room ID | Generated | `?room=abc123` |
| `lang` | Speech recognition language | en-US | `?lang=fr-FR` |
| `label` | Speaker label | None | `?label=Host` |
| `showtime` | Caption display duration (ms) | Auto | `?showtime=5000` |

### 8.2 Translation Parameters
| Parameter | Description | Example |
|-----------|-------------|---------|
| `translate` | Target translation language | `?translate=es` |
| `googlekey` | Google Cloud API key | `?googlekey=KEY` |
| `context` | Enable context translation | `?context=1` |

### 8.3 TTS Parameters
| Parameter | Description | Example |
|-----------|-------------|---------|
| `tts` | Enable TTS with language | `?tts=en-US` |
| `ttsprovider` | TTS provider | `?ttsprovider=google` |
| `ttsstream` | Enable streaming TTS | `?ttsstream=1` |

## 9. Technical Implementation Details

### 9.1 Error Handling
- Network error recovery
- Speech recognition failure handling
- Translation service fallbacks
- WebSocket reconnection logic

### 9.2 Performance Optimizations
- Translation caching
- Model preloading
- Connection pooling
- Efficient DOM updates

### 9.3 Security Considerations
- Insecure room ID detection
- API key exposure warnings
- Content sanitization
- CORS handling

## 10. Development and Deployment

### 10.1 Self-Hosting Requirements
- Static file hosting (GitHub Pages compatible)
- WebSocket server (optional custom deployment)
- HTTPS required for speech recognition

### 10.2 Customization Options
- CSS styling overrides
- Custom fonts via Base64 encoding
- Translation model customization
- TTS provider configuration

This documentation provides a comprehensive overview of the CAPTION.Ninja system architecture and implementation details for developers looking to understand, modify, or extend the codebase.
