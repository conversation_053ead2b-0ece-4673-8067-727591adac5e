<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-time Translation App</title>
    <style>
        @font-face {
            font-family: 'Cousine';
            src: url('fonts/Cousine-Bold.ttf') format('truetype');
        }
        
        :root {
            --primary-color: #2498Eb;
            --secondary-color: #2c3e50;
            --background-color: #e6e6e970;
            --text-color: #333;
            --border-radius: 4px;
            --cc-background: #000000;
            --cc-text-color: #ffffff;
            --cc-font-size: 24px;
            --cc-line-height: 1.2;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
            margin: 0;
            padding: 0
        }
		
		#apiKey {
			font-size: 20px;
		}

        h2 {
            color: var(--secondary-color);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
        }

        a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        a:hover {
            color: var(--secondary-color);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0
        }

        .control-panel {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            align-items: center;
        }

        .language-selector {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .output {
            font-family: Cousine, monospace;
            color: black;
            min-height: 120px;
            font-size: 2.5em;
            line-height: 1.1em;
            letter-spacing: 0.0em;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: var(--border-radius);
            background-color: #f9f9f9;
        }

        .output span { 
            background-color: black; 
            padding: 8px 8px 0px 8px;
            margin: 0;
            color: white;
        }

        select, input[type="checkbox"], input[type="text"] {
            margin: 5px 0;
            padding: 8px;
            border-radius: var(--border-radius);
            border: 1px solid #ddd;
        }

        input[type="text"] {
            width: 300px;
        }

        label {
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            height: 70px;
            resize: vertical;
            margin-bottom: 15px;
        }
        
        #status {
            font-style: italic;
            color: var(--secondary-color);
        }

        .api-key-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }

        .api-key-status {
            display: inline-block;
            padding: 5px 22px;
            border-radius: var(--border-radius);
            font-weight: bold;
        }

        .status-inactive {
            background-color: #ffeeba;
            color: #856404;
        }

        .status-active {
            background-color: #d4edda;
            color: #155724;
        }

        .button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 8px 26px;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.3s ease;
        }

        .button:hover {
            background-color: var(--secondary-color);
        }

        .button.paused {
            background-color: #dc3545;
        }
		
		#saveApiKey, #copyLink {
			margin-left: 20px;
		}

        .error-message {
            color: #dc3545;
            font-weight: bold;
            margin: 10px 0;
        }

        .hidden {
            display: none;
        }

        .overlay-link {
            margin: 15px 0;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: var(--border-radius);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .control-panel {
                flex-direction: column;
                align-items: flex-start;
            }
            
            input[type="text"] {
                width: 100%;
            }
        }
		
		#customization-info {
			margin: 20px 0;
			padding: 15px;
			background-color: #efefef;
			border-radius: var(--border-radius);
			border-left: 4px solid var(--primary-color);
		}

		#customization-info h4 {
			margin-top: 0;
			color: var(--secondary-color);
		}

		#customization-info ul {
			margin-bottom: 0;
		}

		#customization-info code {
			background-color: #e9e9e9;
			padding: 2px 5px;
			border-radius: 3px;
			font-family: monospace;
		}

		footer {
			margin-top: 30px;
			padding-top: 20px;
			border-top: 1px solid #eee;
			text-align: center;
			font-size: 0.9em;
			color: #666;
		}

		footer a {
			color: var(--primary-color);
		}
    </style>
</head>
<body>
    <div class="container">
		<h2>Real-time Translation App - Premium Version</h2>
		
		<p>This app uses speech recognition to transcribe your voice and translate it in real-time.</p>
		<p>The translation is powered by Google Cloud Translation, which <a href='https://console.cloud.google.com/apis/api/translate.googleapis.com/credentials' target="_blank">requires an API key -- get yours here.</a> <br /> <a href='./translate'>There is non-premium version for free of course also, but it's quite a bit more limited.</a> Check out more options and details on <a href='https://github.com/steveseguin/captionninja'>GitHub</a>.</p>
		<p>To use this app, accept the microphone permissions on page load and then just say something outloud to see the live Transciption. Once that is working, ensure you have your Google API key enteed, and then just enable the Translation Output. The overlay link will mirror the output.</p>
		<div class="api-key-container">
			<label for="apiKey">Google Translation API Key:</label>
			<input type="password" id="apiKey" placeholder="Enter your API key here">
			<button id="saveApiKey" class="button">Save Key</button>
			<span id="apiKeyStatus" class="api-key-status status-inactive">No API Key</span>
		</div>
		
		<p><strong>Important:</strong> Only open one instance of this tool at a time to avoid microphone access conflicts.</p>
		
		<div class="control-panel">
			<div class="language-selector">
				<label for="langFrom">From:</label>
				<select id="langFrom" title="Set input language via the URL `&lang=en-US` option"></select>
			</div>
			
			<div class="language-selector">
				<label for="langTo">To:</label>
				<select id="langTo"></select>
			</div>
			
			<label>
				<input type="checkbox" id="fullContext">
				Translate with added context
			</label>
			
			<label>
				<input type="checkbox" id="enabledTranscription">
				<span style="color:green;"><b>Start</b></span> Translation Output (💸)
			</label>
			
			<label>
				<input type="checkbox" id="incrementalUpdates">
				Enable incremental updates (💸💸💸)
			</label>
		</div>
		<div class="control-panel">
			<button id="toggleTranscription" class="button">Pause Transcription</button>
			<div id="status"></div>
			<div id="errorContainer" class="error-message hidden"></div>
		</div>
		
		
		<textarea id="input" placeholder="Transcribed text will appear here..."></textarea>
		
		<div id="output" class="output">Translated text will appear here...</div>
		
		<p>Please note that this app uses your default microphone as the audio input source. 
		You sometimes can change the default audio source via the browser's setting, but you can also change it at your system level by changing the default recording device. 
		You can also change audio sources by using a Virtual Audio Cable, <a href='https://www.vb-audio.com/Cable/'> such as this one.</a> 
		Using it, it becomes possible to select other sources, including microphones, speakers, and other applications.</p>
		
		
		<div class="overlay-link">
			Overlay URL (for OBS): <a id="shareLink" href="#" target="_blank">Loading...</a>
			<button id="copyLink" class="button">Copy Link</button>
		</div>
		<div id="customization-info">
			<h4>Customizing the Overlay</h4>
			<p>The overlay page supports several URL parameters for customization:</p>
			<ul>
				<li><code>clear=1</code> - Clear existing captions when new ones arrive</li>
				<li><code>showtime=5000</code> - Set timeout for captions in milliseconds</li>
				<li><code>html=1</code> - Allow HTML in captions (use with caution)</li>
				<li><code>speech=1</code> - Enable text-to-speech for captions</li>
			</ul>
		</div>
		
		
		
		<footer>
			<p>Made with ❤️ by <a href="https://github.com/steveseguin" target="_blank">Steve Seguin</a>. Find this project on <a href="https://github.com/steveseguin/captionninja" target="_blank">GitHub</a>.</p>
			<p>A free alternative version of this tool available at <a href="https://caption.ninja/translate" target="_blank">caption.ninja/translate</a>.</p>
		</footer>
	</div>

    <script src="security-utils.js"></script>
    <script>
	
	
	
	
	
	var generateRandomString = function (LLL = 16) {
		var text = "";
		var words = ["the", "of", "to", "and", "a", "in", "is", "it", "you", "that", "he", "was", "for", "on", "are", "with", "as", "I", "his", "they", "be", "at", "one", "have", "this", "from", "or", "had", "by", "word", "but", "what", "some", "we", "can", "out", "other", "were", "all", "there", "when", "up", "use", "your", "how", "said", "an", "each", "she", "which", "do", "their", "time", "if", "will", "way", "about", "many", "then", "them", "write", "would", "like", "so", "these", "her", "long", "make", "thing", "see", "him", "two", "has", "look", "more", "day", "could", "go", "come", "did", "number", "sound", "no", "most", "people", "my", "over", "know", "water", "than", "call", "first", "who", "may", "down", "side", "been", "now", "find", "any", "new", "work", "part", "take", "get", "place", "made", "live", "where", "after", "back", "little", "only", "round", "man", "year", "came", "show", "every", "good", "me", "give", "our", "under", "name", "very", "through", "just", "form", "sentence", "great", "think", "say", "help", "low", "line", "differ", "turn", "cause", "much", "mean", "before", "move", "right", "boy", "old", "too", "same", "tell", "does", "set", "three", "want", "air", "well", "also", "play", "small", "end", "put", "home", "read", "hand", "port", "large", "spell", "add", "even", "land", "here", "must", "big", "high", "such", "follow", "act", "why", "ask", "men", "change", "went", "light", "kind", "off", "need", "house", "picture", "try", "us", "again", "animal", "point", "mother", "world", "near", "build", "self", "earth", "father", "head", "stand", "own", "page", "should", "country", "found", "answer", "school", "grow", "study", "still", "learn", "plant", "cover", "food", "sun", "four", "between", "state", "keep", "eye", "never", "last", "let", "thought", "city", "tree", "cross", "farm", "hard", "start", "might", "story", "saw", "far", "sea", "draw", "left", "late", "run", "dont", "while", "press", "close", "night", "real", "life", "few", "north", "open", "seem", "together", "next", "white", "children", "begin", "got", "walk", "example", "ease", "paper", "group", "always", "music", "those", "both", "mark", "often", "letter", "until", "mile", "river", "car", "feet", "care", "second", "book", "carry", "took", "science", "eat", "room", "friend", "began", "idea", "fish", "mountain", "stop", "once", "base", "hear", "horse", "cut", "sure", "watch", "color", "face", "wood", "main", "enough", "plain", "girl", "usual", "young", "ready", "above", "ever", "red", "list", "though", "feel", "talk", "bird", "soon", "body", "dog", "family", "direct", "pose", "leave", "song", "measure", "door", "product", "black", "short", "numeral", "class", "wind", "question", "happen", "complete", "ship", "area", "half", "rock", "order", "fire", "south", "problem", "piece", "told", "knew", "pass", "since", "top", "whole", "king", "space", "heard", "best", "hour", "better", "during", "hundred", "five", "remember", "step", "early", "hold", "west", "ground", "interest", "reach", "fast", "verb", "sing", "listen", "six", "table", "travel", "less", "morning", "ten", "simple", "several", "vowel", "toward", "war", "lay", "against", "pattern", "slow", "center", "love", "person", "money", "serve", "appear", "road", "map", "rain", "rule", "govern", "pull", "cold", "notice", "voice", "unit", "power", "town", "fine", "certain", "fly", "fall", "lead", "cry", "dark", "machine", "note", "wait", "plan", "figure", "star", "box", "noun", "field", "rest", "correct", "able", "pound", "done", "beauty", "drive", "stood", "contain", "front", "teach", "week", "final", "gave", "green", "oh", "quick", "develop", "ocean", "warm", "free", "minute", "strong", "special", "mind", "behind", "clear", "tail", "produce", "fact", "street", "inch", "multiply", "nothing", "course", "stay", "wheel", "full", "force", "blue", "object", "decide", "surface", "deep", "moon", "island", "foot", "system", "busy", "test", "record", "boat", "common", "gold", "possible", "plane", "stead", "dry", "wonder", "laugh", "thousand", "ago", "ran", "check", "game", "shape", "equate", "hot", "miss", "brought", "heat", "snow", "tire", "bring", "yes", "distant", "fill", "east", "paint", "language", "among", "grand", "ball", "yet", "wave", "drop", "heart", "am", "present", "heavy", "dance", "engine", "position", "arm", "wide", "sail", "material", "size", "vary", "settle", "speak", "weight", "general", "ice", "matter", "circle", "pair", "include", "divide", "syllable", "felt", "perhaps", "pick", "sudden", "count", "square", "reason", "length", "represent", "art", "subject", "region", "energy", "hunt", "probable", "bed", "brother", "egg", "ride", "cell", "believe", "fraction", "forest", "sit", "race", "window", "store", "summer", "train", "sleep", "prove", "lone", "leg", "exercise", "wall", "catch", "mount", "wish", "sky", "board", "joy", "winter", "sat", "written", "wild", "instrument", "kept", "glass", "grass", "cow", "job", "edge", "sign", "visit", "past", "soft", "fun", "bright", "gas", "weather", "month", "million", "bear", "finish", "happy", "hope", "flower", "clothe", "strange", "gone", "jump", "baby", "eight", "village", "meet", "root", "buy", "raise", "solve", "metal", "whether", "push", "seven", "paragraph", "third", "shall", "held", "hair", "describe", "cook", "floor", "either", "result", "burn", "hill", "safe", "cat", "century", "consider", "type", "law", "bit", "coast", "copy", "phrase", "silent", "tall", "sand", "soil", "roll", "temperature", "finger", "industry", "value", "fight", "lie", "beat", "excite", "natural", "view", "sense", "ear", "else", "quite", "broke", "case", "middle", "kill", "son", "lake", "moment", "scale", "loud", "spring", "observe", "child", "straight", "consonant", "nation", "dictionary", "milk", "speed", "method", "organ", "pay", "age", "section", "dress", "cloud", "surprise", "quiet", "stone", "tiny", "climb", "cool", "design", "poor", "lot", "experiment", "bottom", "key", "iron", "single", "stick", "flat", "twenty", "skin", "smile", "crease", "hole", "trade", "melody", "trip", "office", "receive", "row", "mouth", "exact", "symbol", "die", "least", "trouble", "shout", "except", "wrote", "seed", "tone", "join", "suggest", "clean", "break", "lady", "yard", "rise", "bad", "blow", "oil", "blood", "touch", "grew", "cent", "mix", "team", "wire", "cost", "lost", "brown", "wear", "garden", "equal", "sent", "choose", "fell", "fit", "flow", "fair", "bank", "collect", "save", "control", "decimal", "gentle", "woman", "captain", "practice", "separate", "difficult", "doctor", "please", "protect", "noon", "whose", "locate", "ring", "character", "insect", "caught", "period", "indicate", "radio", "spoke", "atom", "human", "history", "effect", "electric", "expect", "crop", "modern", "element", "hit", "student", "corner", "party", "supply", "bone", "rail", "imagine", "provide", "agree", "thus", "capital", "wont", "chair", "danger", "fruit", "rich", "thick", "soldier", "process", "operate", "guess", "necessary", "sharp", "wing", "create", "neighbor", "wash", "bat", "rather", "crowd", "corn", "compare", "poem", "string", "bell", "depend", "meat", "rub", "tube", "famous", "dollar", "stream", "fear", "sight", "thin", "triangle", "planet", "hurry", "chief", "colony", "clock", "mine", "tie", "enter", "major", "fresh", "search", "send", "yellow", "gun", "allow", "print", "dead", "spot", "desert", "suit", "current", "lift", "rose", "continue", "block", "chart", "hat", "sell", "success", "company", "subtract", "event", "particular", "deal", "swim", "term", "opposite", "wife", "shoe", "shoulder", "spread", "arrange", "camp", "invent", "cotton", "born", "determine", "quart", "nine", "truck", "noise", "level", "chance", "gather", "shop", "stretch", "throw", "shine", "property", "column", "molecule", "select", "wrong", "gray", "repeat", "require", "broad", "prepare", "salt", "nose", "plural", "anger", "claim", "continent", "oxygen", "sugar", "death", "pretty", "skill", "women", "season", "solution", "magnet", "silver", "thank", "branch", "match", "suffix", "especially", "fig", "afraid", "huge", "sister", "steel", "discuss", "forward", "similar", "guide", "experience", "score", "apple", "bought", "led", "pitch", "coat", "mass", "card", "band", "rope", "slip", "win", "dream", "evening", "condition", "feed", "tool", "total", "basic", "smell", "valley", "nor", "double", "seat", "arrive", "master", "track", "parent", "shore", "division", "sheet", "substance", "favor", "connect", "post", "spend", "chord", "fat", "glad", "original", "share", "station", "dad", "bread", "charge", "proper", "bar", "offer", "segment", "slave", "duck", "instant", "market", "degree", "populate", "chick", "dear", "enemy", "reply", "drink", "occur", "support", "speech", "nature", "range", "steam", "motion", "path", "liquid", "log", "meant", "quotient", "teeth", "shell", "neck"];

		for (var i = 0; i < 2; i++) {
			try {
				var rndint = parseInt(Math.random() * 1000);
				text += words[rndint]; // capitalizeFirstLetter can be used to improve security
			} catch (e) {}
		}
		var possible = "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789";
		text += possible.charAt(Math.floor(Math.random() * possible.length));
		while (text.length < LLL) {
			text += possible.charAt(Math.floor(Math.random() * possible.length));
		}
		try {
			text = text.replaceAll("AD", "vDAv"); // avoiding adblockers
			text = text.replaceAll("Ad", "vdAv");
			text = text.replaceAll("ad", "vdav");
			text = text.replaceAll("aD", "vDav");
		} catch (e) {
			console.error(e);
		}

		return text;
	};
	
	
	const app = {
            state: {
                apiKey: '',
                roomId: '',
                myLang: navigator.language || 'en-US',
                targetLang: 'en',
                isPaused: false,
                isTranscribing: false,
                languageData: [],
                lastTranscription: '',
                secondTranscription: '',
                finalTranscript: '',
                counter: 0,
                activeRequest: null,
                lastRequestTime: 0,
                languageMap: {}
            },
            elements: {
                apiKeyInput: document.getElementById('apiKey'),
                apiKeyStatus: document.getElementById('apiKeyStatus'),
                saveApiKeyBtn: document.getElementById('saveApiKey'),
                langFromSelect: document.getElementById('langFrom'),
                langToSelect: document.getElementById('langTo'),
                fullContextCheckbox: document.getElementById('fullContext'),
                enabledTranscriptionCheckbox: document.getElementById('enabledTranscription'),
                incrementalUpdatesCheckbox: document.getElementById('incrementalUpdates'),
                toggleTranscriptionBtn: document.getElementById('toggleTranscription'),
                inputTextarea: document.getElementById('input'),
                outputDiv: document.getElementById('output'),
                statusDiv: document.getElementById('status'),
                shareLink: document.getElementById('shareLink'),
                copyLinkBtn: document.getElementById('copyLink'),
                errorContainer: document.getElementById('errorContainer')
            },
            recognition: null,
            socket: null,
            retryCount: 0,
            init: function() {
                this.loadApiKey();
                this.generateRoomId();
                this.loadLanguagePreferences();
                this.setupEventListeners();
                this.fetchLanguages();
                this.connectWebSocket();
                this.updateShareLink();
            },
            loadApiKey: function() {
                const savedKey = this.getStorage('apiKey');
                if (savedKey) {
                    this.state.apiKey = savedKey;
                    this.elements.apiKeyInput.value = savedKey;
                    this.updateApiKeyStatus(true);
                } else {
                    this.updateApiKeyStatus(false);
                }
            },
            updateApiKeyStatus: function(isActive) {
                if (isActive) {
                    this.elements.apiKeyStatus.textContent = 'API Key Active';
                    this.elements.apiKeyStatus.className = 'api-key-status status-active';
                } else {
                    this.elements.apiKeyStatus.textContent = 'No API Key';
                    this.elements.apiKeyStatus.className = 'api-key-status status-inactive';
                }
            },
            generateRoomId: function() {
                const urlParams = new URLSearchParams(window.location.search);
                if (urlParams.has('room')) {
                    this.state.roomId = urlParams.get('room');
                } else {
                    this.state.roomId = generateRandomString();
                    this.updateURL('room=' + this.state.roomId);
                }
                
                // Check for insecure stream ID and show warning
                if (isInsecureStreamId(this.state.roomId)) {
                    injectSecurityStyles();
                    showSecurityWarning(
                        'Warning: You are using an insecure room ID that could be easily guessed. Consider using a more secure ID for privacy.',
                        0 // No auto-dismiss, user must close manually
                    );
                }
            },
            generateRandomId: function() {
                const possible = "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789";
                let text = "";
                for (let i = 0; i < 7; i++) {
                    text += possible.charAt(Math.floor(Math.random() * possible.length));
                }
                return text;
            },
            loadLanguagePreferences: function() {
                const urlParams = new URLSearchParams(window.location.search);
                if (urlParams.has('lang')) {
                    this.state.myLang = urlParams.get('lang');
                } else if (this.getStorage('myLang')) {
                    this.state.myLang = this.getStorage('myLang');
                } else {
                    this.updateURL('lang=' + this.state.myLang);
                }
                this.state.myLangCode = this.state.myLang.split('-')[0].toLowerCase();
                if (urlParams.has('translate') || urlParams.has('target')) {
                    this.state.targetLang = urlParams.get('translate') || urlParams.get('target');
                } else if (this.getStorage('targetLang')) {
                    this.state.targetLang = this.getStorage('targetLang');
                } else {
                    this.state.targetLang = this.state.myLangCode === 'en' ? 'de' : 'en';
                    this.updateURL('translate=' + this.state.targetLang);
                }
                this.setStorage('targetLang', this.state.targetLang, 999999);
                this.setStorage('myLang', this.state.myLang, 999999);
            },
            setupEventListeners: function() {
                this.elements.saveApiKeyBtn.addEventListener('click', () => {
					const newKey = this.elements.apiKeyInput.value.trim();
					if (newKey) {
						if (newKey !== this.state.apiKey) {
							this.state.apiKey = newKey;
							this.setStorage('apiKey', newKey, 999999);
							this.updateApiKeyStatus(true);
							this.fetchLanguages();
						} else {
							const originalText = this.elements.saveApiKeyBtn.textContent;
							this.elements.saveApiKeyBtn.textContent = 'Key Already Active';
							setTimeout(() => {
								this.elements.saveApiKeyBtn.textContent = originalText;
							}, 2000);
						}
					} else {
						this.showError('Please enter a valid API key');
					}
				});
                this.elements.copyLinkBtn.addEventListener('click', () => {
                    navigator.clipboard.writeText(this.elements.shareLink.href)
                        .then(() => {
                            this.elements.copyLinkBtn.textContent = 'Copied!';
                            setTimeout(() => {
                                this.elements.copyLinkBtn.textContent = 'Copy Link';
                            }, 2000);
                        })
                        .catch(err => {
                            this.showError('Failed to copy: ' + err);
                        });
                });
                this.elements.langFromSelect.addEventListener('change', (e) => {
                    const newLang = e.target.value;
                    this.state.myLang = newLang;
                    this.state.myLangCode = newLang.split('-')[0].toLowerCase();
                    if (this.state.myLangCode === this.state.targetLang) {
                        this.state.targetLang = (this.state.myLangCode === 'en') ? 'de' : 'en';
                        this.updateLanguageSelectors();
                    }
                    this.updateURL('lang=' + this.state.myLang, true);
                    this.setStorage('myLang', this.state.myLang, 999999);
                    this.restartRecognition();
                });
                this.elements.langToSelect.addEventListener('change', (e) => {
                    const newTarget = e.target.value;
                    if (newTarget === this.state.myLangCode) {
                        this.showError('Source and target languages cannot be the same');
                        this.updateLanguageSelectors();
                        return;
                    }
                    this.state.targetLang = newTarget;
                    this.updateURL('translate=' + this.state.targetLang, true);
                    this.setStorage('targetLang', this.state.targetLang, 999999);
                    const currentText = this.elements.inputTextarea.value.trim();
                    if (currentText && this.elements.enabledTranscriptionCheckbox.checked) {
                        this.translateText(currentText);
                    }
                });
                this.elements.toggleTranscriptionBtn.addEventListener('click', () => {
                    this.state.isPaused = !this.state.isPaused;
                    if (this.state.isPaused) {
                        if (this.recognition) {
                            this.recognition.stop();
                        }
                        this.elements.toggleTranscriptionBtn.textContent = 'Resume Transcription';
                        this.elements.toggleTranscriptionBtn.classList.add('paused');
                    } else {
                        this.restartRecognition();
                        this.elements.toggleTranscriptionBtn.textContent = 'Pause Transcription';
                        this.elements.toggleTranscriptionBtn.classList.remove('paused');
                    }
                });
                this.elements.incrementalUpdatesCheckbox.addEventListener('change', () => {
                    this.restartRecognition();
                });
            },
            connectWebSocket: function() {
                if (this.socket) {
                    this.socket.close();
                    this.socket = null;
                }
                this.socket = new WebSocket("wss://api.caption.ninja:443");
                this.socket.onclose = () => {
                    console.log("WebSocket connection closed. Attempting to reconnect...");
                    const delay = this.getRetryDelay();
                    console.log(`Reconnecting in ${delay}ms (attempt ${this.retryCount + 1})...`);
                    setTimeout(() => this.connectWebSocket(), delay);
                    this.retryCount++;
                };
                this.socket.onopen = () => {
                    console.log("WebSocket connected. Joining room...");
                    this.retryCount = 0;
                    this.socket.send(JSON.stringify({"join": this.state.roomId}));
                };
                this.socket.onerror = (error) => {
                    console.error("WebSocket error:", error);
                    if (this.socket) {
                        this.socket.close();
                        this.socket = null;
                    }
                };
            },
			handleRecognitionConflict: function() {
				this.state.isPaused = true;
				this.elements.toggleTranscriptionBtn.textContent = 'Resume Transcription';
				this.elements.toggleTranscriptionBtn.classList.add('paused');
				if (this.recognition) {
					this.recognition.onend = null;
					this.recognition.stop();
					this.recognition = null;
				}
				this.showError('Speech recognition is already in use in another tab or application. Click "Resume Transcription" when ready to try again.');
				this.elements.statusDiv.textContent = "Paused: Recognition conflict detected";
			},
            getRetryDelay: function() {
                if (this.retryCount === 0) {
                    return 0;
                }
                return Math.min(1000 * Math.pow(2, this.retryCount - 1), 30000);
            },
            fetchLanguages: function() {
                let url;
                if (this.state.apiKey) {
                    url = `https://www.googleapis.com/language/translate/v2/languages?key=${this.state.apiKey}`;
                } else {
                    url = `https://captionninjapremium.vdo.workers.dev/?ts=${Date.now()}`;
                }
                fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.error) {
                        throw new Error(data.error.message || 'Error fetching languages');
                    }
                    this.processLanguages(data.data.languages);
                })
                .catch(error => {
                    this.showError('Failed to fetch languages: ' + error.message);
                    console.error('Language fetch error:', error);
                    this.loadDefaultLanguages();
                });
            },
            processLanguages: function(languages) {
                this.state.languageData = languages;
                languages.forEach(lang => {
                    const langName = this.getLanguageName(lang.language);
                    this.state.languageMap[lang.language] = langName;
                });
                this.populateLanguageSelectors();
                this.setupSpeechRecognition();
            },
            getLanguageName: function(code) {
                const languageNames = {
					"aa": "Afar", "ab": "Abkhazian", "ae": "Avestan", "af": "Afrikaans",
					"ak": "Akan", "am": "Amharic", "an": "Aragonese", "ar": "Arabic",
					"as": "Assamese", "av": "Avaric", "ay": "Aymara", "az": "Azerbaijani",
					"ba": "Bashkir", "be": "Belarusian", "bg": "Bulgarian", "bh": "Bihari",
					"bi": "Bislama", "bm": "Bambara", "bn": "Bengali", "bo": "Tibetan",
					"br": "Breton", "bs": "Bosnian", "ca": "Catalan", "ce": "Chechen",
					"ch": "Chamorro", "co": "Corsican", "cr": "Cree", "cs": "Czech",
					"cu": "Church Slavic", "cv": "Chuvash", "cy": "Welsh", "da": "Danish",
					"de": "German", "dv": "Divehi", "dz": "Dzongkha", "ee": "Ewe",
					"el": "Greek", "en": "English", "eo": "Esperanto", "es": "Spanish",
					"et": "Estonian", "eu": "Basque", "fa": "Persian", "ff": "Fulah",
					"fi": "Finnish", "fj": "Fijian", "fo": "Faroese", "fr": "French",
					"fy": "Western Frisian", "ga": "Irish", "gd": "Scottish Gaelic", "gl": "Galician",
					"gn": "Guarani", "gu": "Gujarati", "gv": "Manx", "ha": "Hausa",
					"he": "Hebrew", "hi": "Hindi", "ho": "Hiri Motu", "hr": "Croatian",
					"ht": "Haitian", "hu": "Hungarian", "hy": "Armenian", "hz": "Herero",
					"ia": "Interlingua", "id": "Indonesian", "ie": "Interlingue", "ig": "Igbo",
					"ii": "Sichuan Yi", "ik": "Inupiaq", "io": "Ido", "is": "Icelandic",
					"it": "Italian", "iu": "Inuktitut", "ja": "Japanese", "jv": "Javanese",
					"ka": "Georgian", "kg": "Kongo", "ki": "Kikuyu", "kj": "Kuanyama",
					"kk": "Kazakh", "kl": "Kalaallisut", "km": "Khmer", "kn": "Kannada",
					"ko": "Korean", "kr": "Kanuri", "ks": "Kashmiri", "ku": "Kurdish",
					"kv": "Komi", "kw": "Cornish", "ky": "Kyrgyz", "la": "Latin",
					"lb": "Luxembourgish", "lg": "Ganda", "li": "Limburgan", "ln": "Lingala",
					"lo": "Lao", "lt": "Lithuanian", "lu": "Luba-Katanga", "lv": "Latvian",
					"mg": "Malagasy", "mh": "Marshallese", "mi": "Maori", "mk": "Macedonian",
					"ml": "Malayalam", "mn": "Mongolian", "mr": "Marathi", "ms": "Malay",
					"mt": "Maltese", "my": "Burmese", "na": "Nauru", "nb": "Norwegian Bokmål",
					"nd": "North Ndebele", "ne": "Nepali", "ng": "Ndonga", "nl": "Dutch",
					"nn": "Norwegian Nynorsk", "no": "Norwegian", "nr": "South Ndebele", "nv": "Navajo",
					"ny": "Chichewa", "oc": "Occitan", "oj": "Ojibwa", "om": "Oromo",
					"or": "Oriya", "os": "Ossetian", "pa": "Punjabi", "pi": "Pali",
					"pl": "Polish", "ps": "Pashto", "pt": "Portuguese", "qu": "Quechua",
					"rm": "Romansh", "rn": "Rundi", "ro": "Romanian", "ru": "Russian",
					"rw": "Kinyarwanda", "sa": "Sanskrit", "sc": "Sardinian", "sd": "Sindhi",
					"se": "Northern Sami", "sg": "Sango", "si": "Sinhala", "sk": "Slovak",
					"sl": "Slovenian", "sm": "Samoan", "sn": "Shona", "so": "Somali",
					"sq": "Albanian", "sr": "Serbian", "ss": "Swati", "st": "Southern Sotho",
					"su": "Sundanese", "sv": "Swedish", "sw": "Swahili", "ta": "Tamil",
					"te": "Telugu", "tg": "Tajik", "th": "Thai", "ti": "Tigrinya",
					"tk": "Turkmen", "tl": "Tagalog", "tn": "Tswana", "to": "Tonga",
					"tr": "Turkish", "ts": "Tsonga", "tt": "Tatar", "tw": "Twi",
					"ty": "Tahitian", "ug": "Uighur", "uk": "Ukrainian", "ur": "Urdu",
					"uz": "Uzbek", "ve": "Venda", "vi": "Vietnamese", "vo": "Volapük",
					"wa": "Walloon", "wo": "Wolof", "xh": "Xhosa", "yi": "Yiddish",
					"yo": "Yoruba", "za": "Zhuang", "zh": "Chinese", "zu": "Zulu",
					"ace": "Achinese", "ach": "Acoli", "alz": "Alur", "awa": "Awadhi",
					"ban": "Balinese", "bbc": "Batak Toba", "bem": "Bemba", "bew": "Betawi",
					"bho": "Bhojpuri", "bik": "Bikol", "bts": "Batak Simalungun", "btx": "Batak Karo",
					"bua": "Buriat", "ceb": "Cebuano", "cgg": "Chiga", "chm": "Mari",
					"ckb": "Central Kurdish", "cnh": "Hakha Chin", "crh": "Crimean Tatar", "crs": "Seselwa Creole French",
					"din": "Dinka", "doi": "Dogri", "dov": "Dombe", "gaa": "Ga",
					"gom": "Goan Konkani", "haw": "Hawaiian", "hil": "Hiligaynon", "hmn": "Hmong",
					"hrx": "Hunsrik", "ilo": "Iloko", "iw": "Hebrew", "jw": "Javanese",
					"kri": "Krio", "ktu": "Kituba", "lij": "Ligurian", "lmo": "Lombard",
					"ltg": "Latgalian", "luo": "Luo", "lus": "Lushai", "mai": "Maithili",
					"mak": "Makasar", "min": "Minangkabau", "mni-Mtei": "Manipuri (Meetei Mayek script)", "ms-Arab": "Malay (Arabic script)",
					"new": "Newari", "nso": "Northern Sotho", "nus": "Nuer", "pa-Arab": "Punjabi (Arabic script)",
					"pag": "Pangasinan", "pam": "Pampanga", "pap": "Papiamento", "rom": "Romany",
					"scn": "Sicilian", "shn": "Shan", "szl": "Silesian", "tet": "Tetum",
					"yua": "Yucatec Maya", "yue": "Cantonese", "zh-CN": "Chinese (Simplified)", "zh-TW": "Chinese (Traditional)"
				};
                return languageNames[code] || code;
            },
            loadDefaultLanguages: function() {
                const defaultLanguages = [
                    { language: "en" }, { language: "de" }, { language: "fr" },
                    { language: "es" }, { language: "it" }, { language: "nl" },
                    { language: "pt" }, { language: "ru" }, { language: "zh" },
                    { language: "ja" }, { language: "ko" }, { language: "ar" }
                ];
                this.processLanguages(defaultLanguages);
            },
            populateLanguageSelectors: function() {
                this.elements.langFromSelect.innerHTML = '';
                this.elements.langToSelect.innerHTML = '';
                this.state.languageData.forEach(lang => {
                    const option = document.createElement('option');
                    const langName = this.getLanguageName(lang.language);
                    option.value = lang.language;
                    option.textContent = langName;
                    if (lang.language === this.state.myLangCode) {
                        option.selected = true;
                    }
                    this.elements.langFromSelect.appendChild(option);
                });
                this.state.languageData.forEach(lang => {
                    const option = document.createElement('option');
                    const langName = this.getLanguageName(lang.language);
                    option.value = lang.language;
                    option.textContent = langName;
                    if (lang.language === this.state.targetLang) {
                        option.selected = true;
                    }
                    this.elements.langToSelect.appendChild(option);
                });
                this.updateLanguageSelectors();
            },
            updateLanguageSelectors: function() {
                if (this.state.myLangCode === this.state.targetLang) {
                    this.state.targetLang = (this.state.myLangCode === 'en') ? 'de' : 'en';
                }
                const fromOptions = this.elements.langFromSelect.options;
                for (let i = 0; i < fromOptions.length; i++) {
                    if (fromOptions[i].value === this.state.myLangCode) {
                        fromOptions[i].selected = true;
                        break;
                    }
                }
                const toOptions = this.elements.langToSelect.options;
                for (let i = 0; i < toOptions.length; i++) {
                    if (toOptions[i].value === this.state.targetLang) {
                        toOptions[i].selected = true;
                        break;
                    }
                }
            },
            updateShareLink: function() {
                const baseUrl = window.location.href.split('?')[0];
                const overlayUrl = baseUrl.replace(/\/[^\/]*$/, '/overlay') +
                                  `?room=${this.state.roomId}`;
                this.elements.shareLink.href = overlayUrl;
                this.elements.shareLink.textContent = overlayUrl;
            },
            setupSpeechRecognition: function() {
				if (!('webkitSpeechRecognition' in window)) {
					this.showError('Speech recognition is not supported in your browser. Try Chrome or Edge.');
					return;
				}
				this.recognition = new webkitSpeechRecognition();
				this.recognition.lang = this.state.myLang;
				this.recognition.continuous = true;
				this.recognition.interimResults = this.elements.incrementalUpdatesCheckbox.checked;
                this.recognition.onstart = () => {
                    console.log("Speech recognition started");
                    this.elements.statusDiv.textContent = "Listening...";
                    this.state.isTranscribing = true;
                };
                this.recognition.onerror = (event) => {
					console.error("Recognition error:", event);
					if (event.error === 'no-speech') {
						this.elements.statusDiv.textContent = "No speech detected. Still listening...";
					} else if (event.error === 'audio-capture') {
						this.showError("No microphone detected");
					} else if (event.error === 'not-allowed') {
						this.showError("Microphone access denied. Please allow microphone access in your browser settings.");
					} else if (event.error === 'aborted') {
						console.log("Recognition aborted");
						if (!this.state.isPaused) {
							this.handleRecognitionConflict();
						}
					} else if (event.error === 'network') {
						this.showError("Network error occurred. Will attempt to reconnect...");
						this.restartRecognition();
					} else {
						this.showError(`Speech recognition error: ${event.error}`);
					}
				};
                this.recognition.onend = () => {
                    console.log("Speech recognition ended");
                    this.processRemainingText();
                    if (!this.state.isPaused && this.state.isTranscribing) {
                        console.log("Restarting recognition...");
                        this.elements.statusDiv.textContent = "Reconnecting...";
                        setTimeout(() => {
                            try {
                                this.recognition.start();
                            } catch (err) {
                                console.error("Error restarting recognition:", err);
                                this.setupSpeechRecognition();
                            }
                        }, 300);
                    } else {
                        this.elements.statusDiv.textContent = "Paused";
                    }
                };
                this.recognition.onresult = (event) => {
                    if (typeof(event.results) === 'undefined') {
                        console.log('Undefined results in event:', event);
                        return;
                    }
                    
                    // Auto-dismiss security warning when we get actual speech results
                    const warningElement = document.getElementById('stream-security-warning');
                    if (warningElement) {
                        warningElement.style.animation = 'fadeOut 0.3s ease-out';
                        setTimeout(() => warningElement.remove(), 300);
                    }
                    
                    this.processSpeechResults(event);
                };
                if (!this.state.isPaused) {
					try {
						this.recognition.start();
					} catch (error) {
						console.error("Error starting recognition:", error);
						if (error.name === 'InvalidStateError') {
							this.handleRecognitionConflict();
						} else {
							this.showError("Failed to start speech recognition");
						}
					}
				}
			},
            processSpeechResults: function(event) {
                let currentBatchFinal = '';
                let currentBatchInterim = '';
                for (let i = event.resultIndex; i < event.results.length; i++) {
                    const transcript = this.sanitizeText(event.results[i][0].transcript);
                    if (event.results[i].isFinal) {
                        currentBatchFinal += (currentBatchFinal ? ' ' : '') + transcript;
                        console.log(`Processing text: ${transcript} Final: true`);
                    } else {
                        currentBatchInterim += (currentBatchInterim ? ' ' : '') + transcript;
                        console.log(`Processing text: ${transcript} Final: false`);
                    }
                }
                if (currentBatchFinal) {
                    this.state.finalTranscript += (this.state.finalTranscript ? ' ' : '') + currentBatchFinal;
                    this.elements.inputTextarea.value = this.state.finalTranscript;
                    this.translateText(this.state.finalTranscript);
                    this.state.secondTranscription = this.state.lastTranscription;
                    this.state.lastTranscription = this.state.finalTranscript.trim().replace(/\.?$/, '.') + ' ';
                    this.state.finalTranscript = '';
                    console.log("Updated context state:", {
                        second: this.state.secondTranscription,
                        last: this.state.lastTranscription
                    });
                }
                else if (currentBatchInterim && this.elements.incrementalUpdatesCheckbox.checked) {
                    const displayText = this.state.finalTranscript +
                                       (this.state.finalTranscript ? ' ' : '') +
                                       currentBatchInterim;
                    this.elements.inputTextarea.value = displayText;
                    if (this.state.activeRequest === null &&
                        (Date.now() - this.state.lastRequestTime > 250)) {
                        this.translateText(displayText, true);
                    }
                }
            },
            processRemainingText: function() {
                const currentText = this.elements.inputTextarea.value.trim();
                if (currentText &&
                    this.elements.enabledTranscriptionCheckbox.checked &&
                    currentText !== this.state.lastTranscription.trim()) {
                    console.log("Processing final text from interrupted recognition:", currentText);
                    this.translateText(currentText);
                    this.state.secondTranscription = this.state.lastTranscription;
                    this.state.lastTranscription = currentText.replace(/\.?$/, '.') + ' ';
                    this.state.finalTranscript = '';
                }
            },
            translateText: function(textToTranslate, isInterim = false) {
                if (!textToTranslate?.trim() || !this.elements.enabledTranscriptionCheckbox.checked) {
                    return;
                }
                if (this.state.myLangCode === this.state.targetLang) {
                    console.warn("Source and target languages are the same:", this.state.myLangCode);
                    this.state.targetLang = (this.state.myLangCode === 'en') ? 'de' : 'en';
                    this.updateLanguageSelectors();
                }
                let contextText = textToTranslate;
                if (this.elements.fullContextCheckbox.checked) {
                    let fullText = [];
                    if (this.state.secondTranscription?.trim()) {
                        fullText.push(this.state.secondTranscription.trim().replace(/\.?$/, '.'));
                    }
                    if (this.state.lastTranscription?.trim()) {
                        fullText.push(this.state.lastTranscription.trim().replace(/\.?$/, '.'));
                    }
                    if (textToTranslate?.trim()) {
                        fullText.push(textToTranslate.trim());
                    }
                    contextText = fullText.join(' ');
                    console.log("Translating with context:", contextText);
                }
                const now = Date.now();
                const timeSinceLastRequest = now - this.state.lastRequestTime;
                if (timeSinceLastRequest < 250) {
                    console.log("Rate limiting in effect, skipping request");
                    return;
                }
                this.state.lastRequestTime = now;
                if (this.state.activeRequest) {
                    this.state.activeRequest.abort();
                    this.state.activeRequest = null;
                }
                const url = this.state.apiKey
                    ? `https://www.googleapis.com/language/translate/v2/?key=${this.state.apiKey}&q=${encodeURIComponent(contextText)}&target=${this.state.targetLang}&source=${this.state.myLangCode}`
                    : `https://captionninjapremium.vdo.workers.dev/?ts=${Date.now()}&q=${encodeURIComponent(contextText)}&target=${this.state.targetLang}&source=${this.state.myLangCode}`;
                const cachedTranslation = this.getCachedTranslation(url);
                if (cachedTranslation) {
                    console.log('Using cached translation');
                    this.updateTranslation(cachedTranslation, isInterim);
                    return;
                }
                const controller = new AbortController();
                this.state.activeRequest = controller;
                const timeoutId = setTimeout(() => {
                    if (this.state.activeRequest === controller) {
                        console.log('Request timeout');
                        controller.abort();
                        this.state.activeRequest = null;
                    }
                }, 3000);
                fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    signal: controller.signal
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (!data?.data?.translations?.[0]) {
                        throw new Error('Invalid response format from translation service');
                    }
                    const translatedText = data.data.translations[0].translatedText;
                    this.cacheTranslation(url, data.data);
                    this.updateTranslation(translatedText, isInterim);
                })
                .catch(error => {
                    if (error.name === 'AbortError') {
                        console.log('Request was aborted');
                        return;
                    }
                    console.error('Translation error:', error);
                    if (error.message.includes('HTTP error! status: 429')) {
                        if (this.elements.incrementalUpdatesCheckbox.checked) {
                            this.elements.incrementalUpdatesCheckbox.checked = false;
                            this.showError('Incremental updates have been disabled due to rate limiting');
                            this.restartRecognition();
                        }
                    }
                    const now = Date.now();
                    if (!window.lastErrorTime || (now - window.lastErrorTime) > 5000) {
                        window.lastErrorTime = now;
                        this.handleTranslationError(error);
                    }
                })
                .finally(() => {
                    clearTimeout(timeoutId);
                    if (this.state.activeRequest === controller) {
                        this.state.activeRequest = null;
                    }
                });
            },
            updateTranslation: function(translatedText, isInterim) {
                window.lastSuccessfulTranslation = translatedText;
                this.elements.outputDiv.innerHTML = this.formatTranslatedText(translatedText);
                this.state.counter += 1;
                if (this.socket && this.socket.readyState === WebSocket.OPEN) {
                    this.socket.send(JSON.stringify({
                        "msg": true,
                        [isInterim ? "interm" : "final"]: translatedText,
                        "id": this.state.counter,
                        "c": this.elements.fullContextCheckbox.checked,
                        "ln": this.state.targetLang
                    }));
                }
            },
            formatTranslatedText: function(text) {
                const segments = text.split(/(?<=\.)\s+/);
                if (segments.length <= 1) {
                    return `<span>${text}</span>`;
                }
                return segments.map(segment => {
                    if (segment.trim()) {
                        return `<span>${segment}</span>`;
                    }
                    return '';
                }).join(' ');
            },
            handleTranslationError: function(error) {
                let errorMessage = '';
                if (error instanceof TypeError && error.message.includes('CORS')) {
                    errorMessage = 'CORS Error: Unable to access the translation service. Check your API key.';
                } else if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
                    errorMessage = 'Network Error: Unable to connect to the translation service.';
                } else if (error.message.includes('HTTP error! status: 403')) {
                    errorMessage = 'Authorization Error: Invalid API key or insufficient permissions.';
                } else if (error.message.includes('HTTP error! status: 429')) {
                    errorMessage = 'Rate Limit Error: Too many requests. Try disabling incremental updates.';
                } else if (error.message.includes('HTTP error! status: 400')) {
                    errorMessage = 'Bad Request: Source and target languages may be the same.';
                } else if (error.message.includes('Invalid response format')) {
                    errorMessage = 'Error: Unexpected response from the translation service.';
                } else {
                    errorMessage = `Translation Error: ${error.message}`;
                }
                this.showError(errorMessage);
            },
            restartRecognition: function() {
				try {
					if (this.recognition) {
						this.recognition.onend = null;
						this.recognition.stop();
						setTimeout(() => {
							this.recognition = null;
							this.setupSpeechRecognition();
						}, 300);
					} else {
						this.setupSpeechRecognition();
					}
				} catch (error) {
					console.error("Error restarting recognition:", error);
					this.showError("Recognition error occurred. Please reload the page if problems persist.");
					this.state.isPaused = true;
					this.elements.toggleTranscriptionBtn.textContent = 'Resume Transcription';
					this.elements.toggleTranscriptionBtn.classList.add('paused');
				}
			},
            showError: function(message) {
                this.elements.errorContainer.textContent = message;
                this.elements.errorContainer.classList.remove('hidden');
                setTimeout(() => {
                    this.elements.errorContainer.classList.add('hidden');
                }, 5000);
            },
            sanitizeText: function(string) {
                const temp = document.createElement('div');
                temp.textContent = string;
                return temp.textContent
                    .substring(0, Math.min(temp.textContent.length, 500))
                    .trim();
            },
            getCachedTranslation: function(url) {
                try {
                    const cached = localStorage.getItem('translation_' + url);
                    if (cached) {
                        const { data, timestamp } = JSON.parse(cached);
                        if (Date.now() - timestamp < 24 * 60 * 60 * 1000) {
                            return data?.translations?.[0]?.translatedText || null;
                        }
                        localStorage.removeItem('translation_' + url);
                    }
                } catch (e) {
                    console.error('Cache error:', e);
                }
                return null;
            },
            cacheTranslation: function(url, data) {
                try {
                    localStorage.setItem('translation_' + url, JSON.stringify({
                        data: data,
                        timestamp: Date.now()
                    }));
                } catch (e) {
                    console.error('Cache storage error:', e);
                    try {
                        const keys = Object.keys(localStorage);
                        for (let key of keys) {
                            if (key.startsWith('translation_')) {
                                localStorage.removeItem(key);
                            }
                        }
                    } catch (e) {
                        console.error('Cache cleanup error:', e);
                    }
                }
            },
            getStorage: function(key) {
                try {
                    const itemStr = localStorage.getItem(key);
                    if (!itemStr) {
                        return null;
                    }
                    const item = JSON.parse(itemStr);
                    const now = new Date();
                    if (now.getTime() > item.expiry) {
                        localStorage.removeItem(key);
                        return null;
                    }
                    return item.value;
                } catch (e) {
                    console.error('Storage error:', e);
                    return null;
                }
            },
            setStorage: function(key, value, hours = 24) {
                try {
                    const now = new Date();
                    const item = {
                        value: value,
                        expiry: now.getTime() + (hours * 60 * 60 * 1000)
                    };
                    localStorage.setItem(key, JSON.stringify(item));
                } catch (e) {
                    console.error('Storage save error:', e);
                }
            },
            updateURL: function(param, replace = false) {
                const urlParams = new URLSearchParams(window.location.search);
                const paramParts = param.split('=');
                const paramName = paramParts[0];
                const paramValue = paramParts.length > 1 ? paramParts[1] : '';
                if (replace || !urlParams.has(paramName)) {
                    urlParams.set(paramName, paramValue);
                    const newUrl = window.location.pathname + '?' + urlParams.toString();
                    window.history.pushState({ path: newUrl }, '', newUrl);
                }
            }
        };
        document.addEventListener('DOMContentLoaded', function() {
            app.init();
        });
    </script>
</body>
</html>						